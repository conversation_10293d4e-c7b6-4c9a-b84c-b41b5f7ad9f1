package tndung.vnfb.smm.cron;

import io.jsonwebtoken.lang.Strings;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.entity.Category;
import tndung.vnfb.smm.entity.Platform;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.repository.tenant.CategoryRepository;
import tndung.vnfb.smm.repository.tenant.PlatformRepository;
import tndung.vnfb.smm.service.PanelNotificationService;
import tndung.vnfb.smm.service.TenantService;
import tndung.vnfb.smm.service.impl.DomainManagerService;
import tndung.vnfb.smm.service.impl.TenantInitializationService;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.ZonedDateTime;
import java.util.List;

@Component
@RequiredArgsConstructor
@Slf4j
@Profile("!local") // Disable in local environment
public class TenantScheduler {

    private final TenantService tenantService;
    private final DomainManagerService domainManagerService;
    private final PlatformRepository platformRepository;
    private final CategoryRepository categoryRepository;
    private final PanelNotificationService panelNotificationService;
    private final TenantInitializationService tenantInitializationService;

    @Value("${server.ip:**************}")
    private String serverIp;

    /**
     * Scheduled task that runs every 15 minutes to check for tenants with "New" status
     * and process them through the domain-manager API
     */
    @Scheduled(fixedRate = 15 * 60 * 1000) // 15 minutes in milliseconds
    public void processNewTenants() {
        log.info("Starting scheduled task to process new tenants");

        try {
            // Find all tenants with "New" status directly from the database
            List<Tenant> newTenants = tenantService.findByStatus(TenantStatus.New);

            log.info("Found {} tenants with 'New' status", newTenants.size());

            // Process each new tenant
            for (Tenant tenant : newTenants) {
                processNewTenant(tenant);
            }
        } catch (Exception e) {
            log.error("Error processing new tenants: {}", e.getMessage(), e);
        }
    }

    /**
     * Scheduled task that runs every 15 minutes to check for tenants with "Nginx80" status
     * and generate SSL certificates for them
     */
    @Scheduled(fixedRate = 15 * 60 * 1000) // 15 minutes in milliseconds
    public void processNginx80Tenants() {
        log.info("Starting scheduled task to process Nginx80 tenants");

        try {
            // Find all tenants with "Nginx80" status directly from the database
            List<Tenant> nginx80Tenants = tenantService.findByStatus(TenantStatus.Nginx80);

            log.info("Found {} tenants with 'Nginx80' status", nginx80Tenants.size());

            // Process each Nginx80 tenant
            for (Tenant tenant : nginx80Tenants) {
                processNginx80Tenant(tenant);
            }
        } catch (Exception e) {
            log.error("Error processing Nginx80 tenants: {}", e.getMessage(), e);
        }
    }

    /**
     * Check if a domain is already pointing to the correct IP address
     *
     * @param domain The domain to check
     * @return true if the domain is pointing to the correct IP, false otherwise
     */
    private boolean isDomainPointingToCorrectIp(String domain) {
        try {
            log.info("Checking if domain {} is pointing to IP {}", domain, serverIp);

            // Try to resolve the domain to an IP address
            InetAddress[] addresses = InetAddress.getAllByName(domain);

            // Check if any of the resolved IPs match our server IP
            for (InetAddress address : addresses) {
                String ip = address.getHostAddress();
                log.info("Domain {} resolved to IP: {}", domain, ip);

                if (serverIp.equals(ip)) {
                    log.info("Domain {} is correctly pointing to server IP {}", domain, serverIp);
                    return false;
                }
            }

            log.warn("Domain {} is not pointing to server IP {}", domain, serverIp);
            return true;
        } catch (UnknownHostException e) {
            log.warn("Could not resolve domain {}: {}", domain, e.getMessage());
            return true;
        }
    }

    /**
     * Process a single tenant with "New" status
     * Adds the domain to domain-manager and sets status to Nginx80 if successful
     *
     * @param tenant The tenant to process
     */
    private void processNewTenant(Tenant tenant) {
        log.info("Processing new tenant: {} ({})", tenant.getDomain(), tenant.getId());

        try {
            log.info("Domain {} is correctly pointing to server IP {}. Proceeding with setup.",
                    tenant.getDomain(), serverIp);

            // Step 1: Add domain to domain-manager using the new service
            boolean domainAdded = domainManagerService.addDomainToDomainManager(tenant);
            if (!domainAdded) {
                setTenantStatusToFailed(tenant, "Failed to add domain to domain-manager");
                return;
            }

            // Update tenant status to Nginx80
            tenant.setStatus(TenantStatus.Nginx80);
            tenantService.save(tenant);
            log.info("Tenant {} added to domain-manager successfully, status updated to Nginx80", tenant.getDomain());

        } catch (Exception e) {
            log.error("Error processing new tenant {}: {}", tenant.getDomain(), e.getMessage(), e);
            setTenantStatusToFailed(tenant, e.getMessage());
        }
    }

    /**
     * Process a single tenant with "Nginx80" status
     * Generates SSL certificate and sets status to Ready if successful
     *
     * @param tenant The tenant to process
     */
    private void processNginx80Tenant(Tenant tenant) {
        log.info("Processing Nginx80 tenant: {} ({})", tenant.getDomain(), tenant.getId());

        try {
            // First check if the domain is already pointing to our server IP
            if (isDomainPointingToCorrectIp(tenant.getDomain())) {
                log.warn("Domain {} is not pointing to server IP {}. Skipping processing.",
                        tenant.getDomain(), serverIp);
                return;
            }

            // Generate SSL certificate using the new service
            boolean sslGenerated = domainManagerService.generateSslCertificate(tenant);
            if (!sslGenerated) {
                setTenantStatusToFailed(tenant, "Failed to generate SSL certificate");
                return;
            }

            // Update tenant status to Ready
            tenant.setStatus(TenantStatus.Active);
            tenant.setSubscriptionStartDate(ZonedDateTime.now());
            tenant.setSubscriptionEndDate(ZonedDateTime.now().plusDays(30));
            tenantService.save(tenant);

            // Initialize default data for tenant using SQL template
            try {
                tenantInitializationService.initializeTenantDefaultData(tenant);
                TenantContext.setCurrentTenant(tenant.getId());

                final Platform platform = platformRepository.save(Platform.
                        builder().name("No network")
                        .icon(Strings.EMPTY)
                        .sort(0)
                        .build());
                categoryRepository.save(Category.builder().name("Other").platform(platform)
                        .sort(0)
                        .build());
                log.info("Successfully initialized default data for tenant: {}", tenant.getDomain());
            } catch (Exception e) {
                log.error("Failed to initialize default data for tenant {}: {}", tenant.getDomain(), e.getMessage());
                // Continue but log the error
            }

            log.info("Tenant {} SSL certificate generated successfully, status updated to Ready", tenant.getDomain());

            // Send success notification
            try {
                panelNotificationService.createTenantSetupSuccessNotification(tenant);
            } catch (Exception notificationException) {
                log.error("Failed to send setup success notification for tenant {}: {}",
                        tenant.getDomain(), notificationException.getMessage());
            }

        } catch (Exception e) {
            log.error("Error processing Nginx80 tenant {}: {}", tenant.getDomain(), e.getMessage(), e);
            setTenantStatusToFailed(tenant, e.getMessage());
        }
    }

    /**
     * Set tenant status to Failed
     *
     * @param tenant The tenant to update
     * @param reason The reason for failure
     */
    private void setTenantStatusToFailed(Tenant tenant, String reason) {
        log.warn("Setting tenant {} status to Failed: {}", tenant.getDomain(), reason);
        tenant.setStatus(TenantStatus.Failed);
        tenantService.save(tenant);

        // Send failure notification
        try {
            panelNotificationService.createTenantSetupFailureNotification(tenant, reason);
        } catch (Exception notificationException) {
            log.error("Failed to send setup failure notification for tenant {}: {}",
                    tenant.getDomain(), notificationException.getMessage());
        }
    }

    /**
     * Sleep for the specified number of milliseconds
     *
     * @param millis The number of milliseconds to sleep
     */
    private void sleep(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Sleep interrupted", e);
        }
    }
}