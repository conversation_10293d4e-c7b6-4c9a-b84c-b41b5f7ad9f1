package tndung.vnfb.smm.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tndung.vnfb.smm.config.SubscriptionProperties;
import tndung.vnfb.smm.config.TenantContext;
import tndung.vnfb.smm.constant.Common;
import tndung.vnfb.smm.constant.enums.Currency;
import tndung.vnfb.smm.constant.enums.Role;
import tndung.vnfb.smm.constant.enums.TenantStatus;
import tndung.vnfb.smm.constant.enums.TransactionType;
import tndung.vnfb.smm.dto.GeneralSettingsDto;
import tndung.vnfb.smm.dto.TenantHierarchyDto;
import tndung.vnfb.smm.dto.request.*;
import tndung.vnfb.smm.dto.response.GUserRes;
import tndung.vnfb.smm.dto.response.TenantHierarchyResponse;
import tndung.vnfb.smm.entity.ApiProvider;
import tndung.vnfb.smm.entity.GUser;
import tndung.vnfb.smm.entity.Tenant;
import tndung.vnfb.smm.exception.DynamicErrorCode;
import tndung.vnfb.smm.exception.IdErrorCode;
import tndung.vnfb.smm.exception.InvalidParameterException;
import tndung.vnfb.smm.helper.CommonHelper;
import tndung.vnfb.smm.repository.nontenant.GUserRepository;
import tndung.vnfb.smm.repository.nontenant.TenantRepository;
import tndung.vnfb.smm.service.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.ZonedDateTime;
import java.util.*;

import static tndung.vnfb.smm.constant.Common.MAIN_TENANT;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantServiceImpl implements TenantService {

    private final TenantRepository tenantRepository;
    private final GUserRepository gUserRepository;
    private final SubscriptionProperties subscriptionProperties;
    private final BalanceService balanceService;
    private final AuthenticationFacade authenticationFacade;
    private final RedisTemplate<String, ChildTenantReq> redisTemplate;
    private final ObjectMapper objectMapper;
    private final JdbcTemplate jdbcTemplate;
    private final UserTenantAccessService userTenantAccessService;
    private final DomainManagerService domainManagerService;
    //private final AdminPanelService adminPanelService;
    @Autowired
    @Lazy
    private TenantService self;
//    @Autowired
//    private ApiProviderService apiProviderService;


    private final ApplicationContext applicationContext;

    // Lazy getter for TenantService to break circular dependency
    private ApiProviderService getApiProviderService() {
        return applicationContext.getBean(ApiProviderService.class);
    }

    private GUserService getGUserService() {
        return applicationContext.getBean(GUserService.class);
    }

    @Cacheable(value = "tenantsByDomain", key = "#domain")
    @Override
    public Optional<Tenant> findByDomain(String domain) {
        return tenantRepository.findByDomain(domain);
    }

    @Override
    @Cacheable(value = "tenantsById", key = "#id", condition = "#id != null")
    public Optional<Tenant> findByTenantId(String id) {
        if (id == null || id.trim().isEmpty()) {
            return Optional.empty();
        }
        return tenantRepository.findById(id);
    }

    @Override
    @Cacheable(value = "mainTenant")
    public Optional<Tenant> findMainTenant() {
        return tenantRepository.findByMainIsTrue();
    }

    @Override
    public List<Tenant> findAll() {
        return tenantRepository.findAll();
    }

    @Override
    public boolean isMainTenantSite() {
        return MAIN_TENANT.equals(TenantContext.getSiteTenant());
    }

    @Override
    public String getTenantId() {
        if (isMainTenantSite()) {
            return TenantContext.getCurrentTenant();
        }
        return TenantContext.getSiteTenant();
    }

    @Override
    public List<Tenant> findByStatus(TenantStatus status) {
        return tenantRepository.findByStatus(status);
    }

    @Override
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public Tenant save(Tenant tenant) {
        return tenantRepository.save(tenant);
    }

    @Override
    public Tenant save(DomainReq req) {
        Tenant tenant = new Tenant();
        tenant.setDomain(req.getDomain());
        tenant.setStatus(req.getStatus() != null ? req.getStatus() : TenantStatus.New);
        tenant.setId(UUID.randomUUID().toString());
        tenant.setApiUrl(req.getApiUrl());
        tenant.setSiteUrl(req.getSiteUrl());
        tenant.setContactEmail(req.getContactEmail());
        tenant.setIsDeleted(false);
        return save(tenant);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById"}, allEntries = true)
    public void delete(String domain) {
        tenantRepository.deleteByDomain(domain);
    }

    @Override
    @Transactional
    @CacheEvict(value = {"tenantsByDomain", "tenantsById", "tenantInfo"}, allEntries = true)
    public void updateDiscountSystemEnabled(boolean enabled) {
        String tenantId = TenantContext.getCurrentTenant();
        Tenant tenant = tenantRepository.findById(tenantId)
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        tenant.setEnableDiscountSystem(enabled);
        tenantRepository.save(tenant);
    }

    @Override
    public List<Tenant> findSubTenants(String parentTenantId) {
        return tenantRepository.findByParentTenantIdAndIsDeletedFalseOrderByCreatedAt(parentTenantId);
    }

    @Override
    public List<Tenant> findAllParentTenants() {
        return tenantRepository.findAllParentTenants();
    }

    @Override
    public List<Tenant> findAllSubTenants() {
        return tenantRepository.findAllSubTenants();
    }

    @Override
    public Optional<Tenant> findParentTenant(String subTenantId) {
        return tenantRepository.findParentTenant(subTenantId);
    }

    @Override
    public TenantHierarchyResponse getParentTenantHierarchy(String subTenantId) {
        Optional<Tenant> parentTenant = findParentTenant(subTenantId);
        if (parentTenant.isPresent()) {
            return convertToHierarchyResponse(parentTenant.get());
        }
        throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
    }


    @Override
    public boolean canCreateSubTenant(String tenantId) {
        Boolean canCreate = tenantRepository.canCreateSubTenant(tenantId);
        return canCreate != null && canCreate;
    }


    @Override
    public boolean isSubTenant(String tenantId) {
        Boolean result = tenantRepository.isSubTenant(tenantId);
        return result != null && result;
    }

    @Override
    @Transactional
    public TenantHierarchyResponse requestChildTenant(ChildTenantReq req) {
        // Get current user first - fail fast if not authenticated
        final GUser currentUser = authenticationFacade.getCurrentUser()
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.USER_NOT_FOUND));

        // Handle existing tenant logic
        handleExistingTenant(req.getDomain());

        Tenant currentTenant = self.findByTenantId(TenantContext.getCurrentTenant())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        // Calculate and process payment
        processSubscriptionPayment(currentTenant, currentUser);

        // Create and save new tenant
        Tenant newTenant = createNewTenant(req, currentUser);

        // Cache request information
        cacheRequestInfo(newTenant.getId(), req, currentUser);

        return convertToHierarchyResponse(tenantRepository.save(newTenant));
    }

    private void handleExistingTenant(String domain) {
        Optional<Tenant> existingTenant = tenantRepository.findByDomain(domain);

        if (existingTenant.isPresent()) {
            Tenant tenant = existingTenant.get();

            // Only allow if tenant was previously rejected
            if (tenant.getStatus() != TenantStatus.Reject) {
                throw new InvalidParameterException(IdErrorCode.DOMAIN_ALREADY_EXISTS);
            }

            // Clean up rejected tenant
            tenantRepository.deleteByDomain(domain);
        }
    }

    private void processSubscriptionPayment(Tenant tenant, GUser currentUser) {
        BigDecimal baseCost = calculatePanelPrice(tenant);

        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                30, baseCost, 30);

        balanceService.deductBalance(
                currentUser,
                baseCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );
    }

    @Override
    @Transactional
    public void processPaymentFromRootToLeaf(Tenant tenant) {
        // Use default renewal days when no days parameter is provided
        processPaymentFromRootToLeaf(tenant, subscriptionProperties.getDefaultRenewalDays());
    }

    @Transactional
    public void processPaymentFromRootToLeaf(Tenant tenant, int days) {
        if (days <= 0) {
            throw new InvalidParameterException(new DynamicErrorCode(4300, "Number of days must be greater than 0", 400));
        }

        List<TenantHierarchyDto> hierarchy = getTenantParentHierarchy(tenant.getId());
        List<TenantHierarchyDto> orderedHierarchy = getHierarchyFromRootToLeaf(hierarchy);

        if (orderedHierarchy.isEmpty()) {
            log.warn("No hierarchy found for tenant {}, skipping payment processing", tenant.getId());
            return;
        }

        // Calculate panel prices for each level in hierarchy with days adjustment
        List<PaymentInfo> paymentInfos = calculateHierarchyPayments(orderedHierarchy, days);

        // Process payments from root to leaf with rollback capability
        List<GUser> processedUsers = new ArrayList<>();
        List<BigDecimal> deductedAmounts = new ArrayList<>();

        try {
            for (PaymentInfo paymentInfo : paymentInfos) {
                // Find the owner user
                GUser owner = gUserRepository.findByIdForAdmin(paymentInfo.getOwnerId())
                        .orElseThrow(() -> new InvalidParameterException(
                                new DynamicErrorCode(4404,
                                        String.format("Owner user not found: %d", paymentInfo.getOwnerId()),
                                        404)));

                // Check if user has sufficient balance before deducting
                if (!hasSufficientBalance(owner, paymentInfo.getAmount())) {
                    log.warn("Insufficient balance for owner {}: required={}, available={}",
                            paymentInfo.getOwnerId(), paymentInfo.getAmount(), owner.getBalance());

                    // Rollback all previous deductions
                    rollbackPayments(processedUsers, deductedAmounts, tenant.getId(), days);

                    throw new InvalidParameterException(IdErrorCode.BALANCE_NOT_ENOUGH);
                }

                // Deduct balance
                String note = String.format("Panel subscription fee for %d days - tenant %s (level %d)",
                        days, paymentInfo.getTenantDomain(), paymentInfo.getHierarchyLevel());

                GUser updatedUser = balanceService.deductBalanceForNewTenant(
                        owner,
                        paymentInfo.getAmount(),
                        tenant.getId(),
                        note
                );

                processedUsers.add(updatedUser);
                deductedAmounts.add(paymentInfo.getAmount());

                log.info("Successfully deducted {} from owner {} for tenant {} (level {})",
                        paymentInfo.getAmount(), paymentInfo.getOwnerId(),
                        paymentInfo.getTenantDomain(), paymentInfo.getHierarchyLevel());
            }

            log.info("Successfully processed payments for all {} levels in hierarchy for tenant {}",
                    paymentInfos.size(), tenant.getId());

        } catch (Exception e) {
            log.error("Error processing payments for tenant {}: {}", tenant.getId(), e.getMessage());
            // Rollback any successful payments
            rollbackPayments(processedUsers, deductedAmounts, tenant.getId(), days);
            throw e;
        }


    }

    private Tenant createNewTenant(ChildTenantReq req, GUser currentUser) {
        Tenant newTenant = new Tenant();
        newTenant.setId(UUID.randomUUID().toString());
        newTenant.setDomain(req.getDomain());
        newTenant.setApiUrl(); // Consider setting a proper API URL here
        newTenant.setParentTenantId(TenantContext.getSiteTenant());
        newTenant.setStatus(TenantStatus.Waiting);
        newTenant.setIsDeleted(false);
        newTenant.setMain(false);
        newTenant.setSiteUrl("https://" + req.getDomain());
        newTenant.setOwnerId(currentUser.getId());

        return newTenant;
    }

    private void cacheRequestInfo(String tenantId, ChildTenantReq req, GUser currentUser) {
        // Update request with current user info
        req.setEmail(currentUser.getEmail());
        req.setPhone(currentUser.getPhone());

        // Cache the request information
        String cacheKey = Common.Redis.DOMAIN_REQUEST_INFO + tenantId;
        redisTemplate.opsForValue().set(cacheKey, req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TenantHierarchyResponse approveSubTenant(String tenantId) {
        final Optional<Tenant> existsTenant = self.findByTenantId(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        final Tenant parrentTenant = self.findByTenantId(existsTenant.get().getParentTenantId())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));

        final Tenant subTenant = existsTenant.get();

        self.processPaymentFromRootToLeaf(subTenant);

        //self.deductUserBalance(subscriptionProperties.getDefaultRenewalDays(), parrentTenant);


        if (subTenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        subTenant.setStatus(TenantStatus.New);
        // create admin user:
        ChildTenantReq childTenantReq = redisTemplate.opsForValue()
                .get(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        if (childTenantReq == null) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }

        GUser owner = getGUserService().findById(subTenant.getOwnerId());

        if (Strings.isBlank(owner.getApiKey())) {
            throw new InvalidParameterException(IdErrorCode.USER_API_SECRET_KEY_MISSING);
        }


        TenantContext.setCurrentTenant(subTenant.getId());
        GUserRes userRes = getGUserService().create(UserReq.builder().userName(childTenantReq.getAdminUserName())
                .password(childTenantReq.getAdminPassword())
                .email(childTenantReq.getEmail())
                .phone(childTenantReq.getPhone())
                .build(), List.of(Role.CHILD_PANEL, Role.USER));


        // create api provider
        ApiProvider apiProvider = new ApiProvider();
        apiProvider.setTenantId(subTenant.getId());
        apiProvider.setName(parrentTenant.getDomain());
        apiProvider.setUrl(String.format("https://%s/api/v2", parrentTenant.getDomain()));
        apiProvider.setSecretKey(owner.getSecretKey());
        apiProvider.setBalance(owner.getBalance());
        apiProvider.setPreviewBalance(owner.getBalance());
        apiProvider.setCurrency(Currency.USD.name());
        getApiProviderService().save(apiProvider);


        userTenantAccessService.grantTenantAccess(userRes.getId(), subTenant.getId());

        // redisTemplate.delete(Common.Redis.DOMAIN_REQUEST_INFO + tenantId);
        final Tenant tenant = tenantRepository.save(subTenant);
        return convertToHierarchyResponse(tenant);

    }


    @Override
    public TenantHierarchyResponse rejectSubTenant(String tenantId) {
        Optional<Tenant> existsTenant = tenantRepository.findById(tenantId);
        if (existsTenant.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        // refund: money


        Tenant tenant = existsTenant.get();
        if (tenant.getStatus() != TenantStatus.Waiting) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        tenant.setStatus(TenantStatus.Reject);
        return convertToHierarchyResponse(tenantRepository.save(tenant));
    }

    @Override
    @Transactional
    public void suspendChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        Tenant tenant = tenantOpt.get();
        // Call domain-manager to suspend domain using the new service
        boolean suspended = domainManagerService.suspendDomain(tenant.getDomain(), tenant.isSubTenant());
        if (!suspended) {
            log.warn("Failed to suspend domain {} via domain-manager, but continuing with database update",
                    tenant.getDomain());
        }

        tenant.setStatus(TenantStatus.Suspended);
        tenantRepository.save(tenant);
    }

    @Override
    @Transactional
    public void activateChildTenants(String tenantId) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();
        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        Tenant tenant = tenantOpt.get();
        // Call domain-manager to enable domain using the new service
        boolean enabled = domainManagerService.enableDomain(tenant.getDomain(), tenant.isSubTenant());
        if (!enabled) {
            log.warn("Failed to enable domain {} via domain-manager, but continuing with database update",
                    tenant.getDomain());
        }

        tenant.setStatus(TenantStatus.Active);
        tenantRepository.save(tenant);
    }

    @Override
    public TenantHierarchyResponse extendChildSubscription(String tenantId, ExtendTenantSubscriptionReq request) {
        List<Tenant> childTenants = findSubTenants(TenantContext.getCurrentTenant());
        Optional<Tenant> tenantOpt = childTenants.stream()
                .filter(t -> t.getId().equals(tenantId)).findFirst();


        if (tenantOpt.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND);
        }
        Tenant tenant = tenantOpt.get();

        self.processPaymentFromRootToLeaf(tenant, request.getExtensionDays());

        ZonedDateTime currentEndDate = tenant.getSubscriptionEndDate();
        ZonedDateTime newEndDate;

        if (currentEndDate == null || currentEndDate.isBefore(ZonedDateTime.now())) {
            newEndDate = ZonedDateTime.now().plusDays(request.getExtensionDays());
        } else {
            newEndDate = currentEndDate.plusDays(request.getExtensionDays());
        }

        tenant.setSubscriptionEndDate(newEndDate);

        if (tenant.getStatus() == TenantStatus.Expired || tenant.getStatus() == TenantStatus.Suspended) {
            tenant.setStatus(TenantStatus.Active);
        }

        Tenant savedTenant = tenantRepository.save(tenant);
        return convertToHierarchyResponse(savedTenant);
    }

    public TenantHierarchyResponse convertToHierarchyResponse(Tenant tenant) {
        return TenantHierarchyResponse.builder()
                .id(tenant.getId())
                .domain(tenant.getDomain())
                .status(tenant.getStatus())
                .apiUrl(tenant.getApiUrl())
                .siteUrl(tenant.getSiteUrl())
                .contactEmail(tenant.getContactEmail())
                .main(tenant.getMain())
                .createdAt(tenant.getCreatedAt())
                .parentTenantId(tenant.getParentTenantId())
                .daysUntilExpiration(tenant.getDaysUntilExpiration())
                .lastRenewalDate(tenant.getLastRenewalDate())
                .subscriptionEndDate(tenant.getSubscriptionEndDate())
                .subscriptionStartDate(tenant.getSubscriptionStartDate())
                .build();
    }

    /**
     * Trừ tiền từ user hiện tại theo cài đặt giá tiền cho số ngày được chỉ định
     * Tính lại giá theo tỷ lệ với 30 ngày mặc định
     *
     * @param days số ngày cần tính phí
     * @throws InvalidParameterException nếu user không đủ tiền
     */
    @Transactional
    public void deductUserBalance(int days, Tenant tenant) {
        if (days <= 0) {
            throw new InvalidParameterException(new DynamicErrorCode(4300, "Number of days must be greater than 0", 400));
        }

        // Lấy user hiện tại
        var currentUser = authenticationFacade.getCurrentUser();

        if (currentUser.isEmpty()) {
            throw new InvalidParameterException(IdErrorCode.USER_NOT_FOUND);
        }

        // Tính giá theo tỷ lệ với 30 ngày mặc định
        BigDecimal baseCost = calculatePanelPrice(tenant);
        int defaultDays = subscriptionProperties.getDefaultRenewalDays();

        BigDecimal calculatedCost = baseCost
                .multiply(BigDecimal.valueOf(days))
                .divide(BigDecimal.valueOf(defaultDays), 2, RoundingMode.HALF_UP);

        // Tạo note cho transaction
        String note = String.format("Subscription fee for %d days (rate: %s/%d days)",
                days, baseCost, defaultDays);

        // Sử dụng BalanceService để trừ tiền
        balanceService.deductBalance(
                currentUser.get(),
                calculatedCost,
                TransactionType.Spent,
                "SUBSCRIPTION",
                note
        );

    }

    @Override
    public List<Tenant> findTenantsByOwnerId(Long ownerId) {
        return tenantRepository.findByOwnerIdAndIsDeletedFalse(ownerId);
    }


    /**
     * Check if string is null, empty, or whitespace
     */
    private boolean isBlankOrEmpty(String value) {
        return value == null || value.trim().isEmpty();
    }

    /**
     * /**
     * Create fallback general settings for parsing errors
     */
    private GeneralSettingsDto createFallbackGeneralSettings() {
        return GeneralSettingsDto.builder()
                .panelPricePercentage(subscriptionProperties.getRenewalCost())
                .build();
    }


    /**
     * Parse general settings from JSON string with proper error handling
     */
    public GeneralSettingsDto parseGeneralSettings(String generalSettingsJson) {
        if (isBlankOrEmpty(generalSettingsJson)) {
            return createDefaultGeneralSettings();
        }

        try {
            return objectMapper.readValue(generalSettingsJson, GeneralSettingsDto.class);
        } catch (Exception e) {
            log.warn("Failed to parse general settings JSON: {}", e.getMessage());
            return createFallbackGeneralSettings();
        }
    }

    /**
     * Calculate panel price based on multi-level tenant hierarchy
     */
    public BigDecimal calculatePanelPrice(Tenant tenant) {
        if (tenant == null) {
            return getMainTenantBasePrice();
        }

        List<TenantHierarchyDto> hierarchy = getTenantParentHierarchy(tenant.getId());

        if (hierarchy.isEmpty()) {
            log.warn("No hierarchy found for tenant {}, using default price", tenant.getId());
            return subscriptionProperties.getRenewalCost();
        }

        return calculateHierarchicalPrice(hierarchy);
    }

    /**
     * Get tenant parent hierarchy using PostgreSQL function
     */
    private List<TenantHierarchyDto> getTenantParentHierarchy(String tenantId) {
        if (tenantId == null) {
            log.warn("Tenant ID is null, returning empty hierarchy");
            return Collections.emptyList();
        }

        try {
            String sql = "SELECT * FROM get_tenant_parent_hierarchy(?)";
            return jdbcTemplate.query(sql, new Object[]{tenantId}, this::mapToTenantHierarchyDto);
        } catch (Exception e) {
            log.error("Error getting tenant hierarchy for tenant {}: {}", tenantId, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * Map ResultSet to TenantHierarchyDto
     */
    private TenantHierarchyDto mapToTenantHierarchyDto(ResultSet rs, int rowNum) throws SQLException {
        return TenantHierarchyDto.builder()
                .tenantId(rs.getString("tenant_id"))
                .tenantDomain(rs.getString("tenant_domain"))
                .parentId(rs.getString("parent_id"))
                .tenantStatus(rs.getInt("tenant_status"))
                .generalSettings(rs.getString("general_settings"))
                .hierarchyLevel(rs.getInt("hierarchy_level"))
                .isRootParent(rs.getBoolean("is_root_parent"))
                .ownerId(rs.getLong("owner_id"))
                .build();
    }


    /**
     * Get current base price for the current tenant context
     */
    public BigDecimal getCurrentBasePriceForCurrentTenant() {
        Tenant currentTenant = getCurrentTenant();
        return calculatePanelPrice(currentTenant.getParentTenant());
    }

    /**
     * Calculate price through tenant hierarchy from root to current
     */
    private BigDecimal calculateHierarchicalPrice(List<TenantHierarchyDto> hierarchy) {
        List<TenantHierarchyDto> orderedHierarchy = getHierarchyFromRootToLeaf(hierarchy);
        BigDecimal currentPrice = getInitialPrice(orderedHierarchy.get(0));

        for (TenantHierarchyDto hierarchyLevel : orderedHierarchy) {
            currentPrice = applyPricePercentage(currentPrice, hierarchyLevel);
        }

        return currentPrice != null ? currentPrice : subscriptionProperties.getRenewalCost();
    }

    /**
     * Apply percentage increase to current price
     */
    private BigDecimal applyPricePercentage(BigDecimal currentPrice, TenantHierarchyDto hierarchyLevel) {
        GeneralSettingsDto settings = parseGeneralSettings(hierarchyLevel.getGeneralSettings());
        BigDecimal percentage = settings.getPanelPricePercentage();

        if (percentage == null || percentage.compareTo(BigDecimal.ZERO) == 0) {
            return currentPrice;
        }

        return CommonHelper.extend(currentPrice, percentage);
    }

    /**
     * Get hierarchy ordered from root (parent) to leaf (current tenant)
     */
    private List<TenantHierarchyDto> getHierarchyFromRootToLeaf(List<TenantHierarchyDto> hierarchy) {
        List<TenantHierarchyDto> orderedHierarchy = new ArrayList<>(hierarchy);
        Collections.reverse(orderedHierarchy);
        return orderedHierarchy;
    }

    /**
     * Get initial price for root tenant in hierarchy
     */
    private BigDecimal getInitialPrice(TenantHierarchyDto rootTenant) {
        if (rootTenant.getParentId() == null) {
            return getMainTenantBasePrice();
        }

        log.warn("Root tenant should not have parent, using fallback price");
        return subscriptionProperties.getRenewalCost();
    }

    /**
     * Get base price from main tenant settings
     */
    private BigDecimal getMainTenantBasePrice() {
        try {
            Tenant mainTenant = findMainTenant()
                    .orElseThrow(() -> new IllegalStateException("Main tenant not found"));
            GeneralSettingsDto mainSettings = parseGeneralSettings(mainTenant.getGeneralSettings());
            return mainSettings.getBasePrice();
        } catch (Exception e) {
            log.error("Failed to get main tenant base price: {}", e.getMessage());
            return subscriptionProperties.getRenewalCost();
        }
    }

    /**
     * Get current tenant from context
     */
    private Tenant getCurrentTenant() {
        return self.findByTenantId(TenantContext.getCurrentTenant())
                .orElseThrow(() -> new InvalidParameterException(IdErrorCode.TENANT_NOT_FOUND));
    }

    /**
     * Create default general settings
     */
    private GeneralSettingsDto createDefaultGeneralSettings() {
        return GeneralSettingsDto.builder()
                .panelPricePercentage(new BigDecimal(subscriptionProperties.getExpirationNotificationDays()))
                .build();
    }

    /**
     * Calculate panel prices for each level in hierarchy
     * Optimized to avoid multiple SQL calls by calculating prices in a single pass
     * Adjusts prices based on the number of days relative to default renewal days
     */
    private List<PaymentInfo> calculateHierarchyPayments(List<TenantHierarchyDto> orderedHierarchy, int days) {
        List<PaymentInfo> paymentInfos = new ArrayList<>();
        BigDecimal currentPrice = getInitialPrice(orderedHierarchy.get(0));
        int defaultDays = subscriptionProperties.getDefaultRenewalDays();

        for (TenantHierarchyDto hierarchyLevel : orderedHierarchy) {
            // Apply price percentage for this level
            currentPrice = applyPricePercentage(currentPrice, hierarchyLevel);

            // Adjust price based on days ratio (similar to deductUserBalance method)
            BigDecimal adjustedPrice = currentPrice
                    .multiply(BigDecimal.valueOf(days))
                    .divide(BigDecimal.valueOf(defaultDays), 2, RoundingMode.HALF_UP);

            // Create payment info for this level's owner
            PaymentInfo paymentInfo = PaymentInfo.builder()
                    .tenantId(hierarchyLevel.getTenantId())
                    .tenantDomain(hierarchyLevel.getTenantDomain())
                    .ownerId(hierarchyLevel.getOwnerId())
                    .amount(adjustedPrice)
                    .hierarchyLevel(hierarchyLevel.getHierarchyLevel())
                    .build();

            paymentInfos.add(paymentInfo);

            log.debug("Calculated payment for tenant {} (level {}): owner={}, baseAmount={}, adjustedAmount={} for {} days",
                    hierarchyLevel.getTenantDomain(), hierarchyLevel.getHierarchyLevel(),
                    hierarchyLevel.getOwnerId(), currentPrice, adjustedPrice, days);
        }

        return paymentInfos;
    }

    /**
     * Check if user has sufficient balance
     */
    private boolean hasSufficientBalance(GUser user, BigDecimal requiredAmount) {
        return user.getBalance().compareTo(requiredAmount) >= 0;
    }

    /**
     * Rollback payments by adding back the deducted amounts
     */
    private void rollbackPayments(List<GUser> processedUsers, List<BigDecimal> deductedAmounts, String tenantId, int days) {
        if (processedUsers.isEmpty()) {
            return;
        }

        log.warn("Rolling back {} payments for tenant {} ({} days)", processedUsers.size(), tenantId, days);

        for (int i = 0; i < processedUsers.size(); i++) {
            try {
                GUser user = processedUsers.get(i);
                BigDecimal amount = deductedAmounts.get(i);

                String rollbackNote = String.format("Rollback: Panel subscription fee for %d days - tenant %s", days, tenantId);

                balanceService.addBalance(
                        user,
                        amount,
                        TransactionType.Refund,
                        "SUBSCRIPTION_ROLLBACK",
                        rollbackNote
                );

                log.info("Rolled back {} for user {} (tenant: {}, {} days)", amount, user.getId(), tenantId, days);

            } catch (Exception rollbackException) {
                log.error("Failed to rollback payment for user {}: {}",
                        processedUsers.get(i).getId(), rollbackException.getMessage());
                // Continue with other rollbacks even if one fails
            }
        }
    }


    /**
     * Inner class to hold payment information for each hierarchy level
     */
    @lombok.Data
    @lombok.Builder
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    private static class PaymentInfo {
        private String tenantId;
        private String tenantDomain;
        private Long ownerId;
        private BigDecimal amount;
        private Integer hierarchyLevel;
    }

}