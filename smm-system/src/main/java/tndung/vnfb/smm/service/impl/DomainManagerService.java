package tndung.vnfb.smm.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;
import tndung.vnfb.smm.entity.Tenant;

/**
 * Service for managing domain-related operations through domain-manager API
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DomainManagerService {

    private final RestTemplate restTemplate;

    @Value("${domain-manager.url:http://domain-manager:3000}")
    private String domainManagerUrl;

    /**
     * Add domain to domain-manager
     *
     * @param tenant The tenant to add
     * @return true if successful, false otherwise
     */
    public boolean addDomainToDomainManager(Tenant tenant) {
        try {
            log.info("Adding domain {} to domain-manager", tenant.getDomain());

            HttpHeaders headers = createJsonHeaders();
            HttpEntity<Tenant> requestEntity = new HttpEntity<>(tenant, headers);

            String url = buildDomainUrl(tenant.getDomain(), tenant.isSubTenant());

            restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("Domain {} added to domain-manager successfully", tenant.getDomain());
            return true;
        } catch (RestClientException e) {
            log.error("Error adding domain {} to domain-manager: {}", tenant.getDomain(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Generate SSL certificate for domain
     *
     * @param tenant The tenant to generate SSL certificate for
     * @return true if successful, false otherwise
     */
    public boolean generateSslCertificate(Tenant tenant) {
        try {
            log.info("Generating SSL certificate for domain {}", tenant.getDomain());

            String url = buildSslUrl(tenant.getDomain(), tenant.isSubTenant());

            restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    null,
                    String.class
            );

            log.info("SSL certificate for domain {} generated successfully", tenant.getDomain());
            return true;
        } catch (RestClientException e) {
            log.error("Error generating SSL certificate for domain {}: {}", tenant.getDomain(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Suspend domain in domain-manager
     *
     * @param domain The domain to suspend
     * @param isSubTenant Whether this is a sub-tenant
     * @return true if successful, false otherwise
     */
    public boolean suspendDomain(String domain, boolean isSubTenant) {
        try {
            log.info("Suspending domain {} in domain-manager", domain);

            HttpHeaders headers = createJsonHeaders();
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            String url = buildSuspendUrl(domain, isSubTenant);

            restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("Domain {} suspended successfully", domain);
            return true;
        } catch (RestClientException e) {
            log.error("Error suspending domain {}: {}", domain, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Enable domain in domain-manager
     *
     * @param domain The domain to enable
     * @param isSubTenant Whether this is a sub-tenant
     * @return true if successful, false otherwise
     */
    public boolean enableDomain(String domain, boolean isSubTenant) {
        try {
            log.info("Enabling domain {} in domain-manager", domain);

            HttpHeaders headers = createJsonHeaders();
            HttpEntity<String> requestEntity = new HttpEntity<>("{}", headers);

            String url = buildEnableUrl(domain, isSubTenant);

            restTemplate.exchange(
                    url,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("Domain {} enabled successfully", domain);
            return true;
        } catch (RestClientException e) {
            log.error("Error enabling domain {}: {}", domain, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Configure DNS for domain (if needed in the future)
     *
     * @param domain The domain to configure DNS for
     * @param serverIp The server IP address
     * @return true if successful, false otherwise
     */
    public boolean configureDns(String domain, String serverIp) {
        try {
            log.info("Configuring DNS for domain {}", domain);

            HttpHeaders headers = createJsonHeaders();
            String requestBody = "{\"ip\":\"" + serverIp + "\"}";
            HttpEntity<String> requestEntity = new HttpEntity<>(requestBody, headers);

            restTemplate.exchange(
                    domainManagerUrl + "/dns/" + domain,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            log.info("DNS for domain {} configured successfully", domain);
            return true;
        } catch (RestClientException e) {
            log.error("Error configuring DNS for domain {}: {}", domain, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check domain status in domain-manager
     *
     * @param domain The domain to check
     * @return domain status or null if error
     */
    public String checkDomainStatus(String domain) {
        try {
            log.info("Checking status for domain {}", domain);

            String response = restTemplate.getForObject(
                    domainManagerUrl + "/domains/" + domain + "/status",
                    String.class
            );

            log.info("Domain {} status: {}", domain, response);
            return response;
        } catch (RestClientException e) {
            log.error("Error checking status for domain {}: {}", domain, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Delete domain from domain-manager
     *
     * @param domain The domain to delete
     * @param isSubTenant Whether this is a sub-tenant
     * @return true if successful, false otherwise
     */
    public boolean deleteDomain(String domain, boolean isSubTenant) {
        try {
            log.info("Deleting domain {} from domain-manager", domain);

            String url = buildDomainUrl(domain, isSubTenant);

            restTemplate.exchange(
                    url,
                    HttpMethod.DELETE,
                    null,
                    String.class
            );

            log.info("Domain {} deleted successfully", domain);
            return true;
        } catch (RestClientException e) {
            log.error("Error deleting domain {}: {}", domain, e.getMessage(), e);
            return false;
        }
    }

    // Helper methods

    /**
     * Create JSON headers for HTTP requests
     */
    private HttpHeaders createJsonHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        return headers;
    }

    /**
     * Build domain URL with conditional query parameter
     */
    private String buildDomainUrl(String domain, boolean isSubTenant) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder
                .fromHttpUrl(domainManagerUrl + "/domains/" + domain);

        if (isSubTenant) {
            uriBuilder.queryParam("isChildPanel", true);
        }

        return uriBuilder.toUriString();
    }

    /**
     * Build SSL URL with conditional query parameter
     */
    private String buildSslUrl(String domain, boolean isSubTenant) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder
                .fromHttpUrl(domainManagerUrl + "/ssl/" + domain);

        if (isSubTenant) {
            uriBuilder.queryParam("isChildPanel", true);
        }

        return uriBuilder.toUriString();
    }

    /**
     * Build suspend URL with conditional query parameter
     */
    private String buildSuspendUrl(String domain, boolean isSubTenant) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder
                .fromHttpUrl(domainManagerUrl + "/domains/" + domain + "/suspend");

        if (isSubTenant) {
            uriBuilder.queryParam("isChildPanel", true);
        }

        return uriBuilder.toUriString();
    }

    /**
     * Build enable URL with conditional query parameter
     */
    private String buildEnableUrl(String domain, boolean isSubTenant) {
        UriComponentsBuilder uriBuilder = UriComponentsBuilder
                .fromHttpUrl(domainManagerUrl + "/domains/" + domain + "/enable");

        if (isSubTenant) {
            uriBuilder.queryParam("isChildPanel", true);
        }

        return uriBuilder.toUriString();
    }
}