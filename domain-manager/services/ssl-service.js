/**
 * SSL certificate management service
 */

const path = require('path');
const fs = require('fs');
const axios = require('axios');
const config = require('../config/constants');
const logger = require('../utils/logger');
const FileUtils = require('../utils/file-utils');
const NginxConfigService = require('./nginx-config-service');

class SSLService {
  constructor() {
    this.isCertbotRunning = false;
  }

  /**
   * Generate SSL certificate for a domain
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object>} Result of the operation
   */
  async generateCertificate(domain, isChildPanel = false) {
    if (this.isCertbotRunning) {
      throw new Error('Another instance of Certbot is already running');
    }

    try {
      this.isCertbotRunning = true;
      const cleanDomain = FileUtils.cleanDomainName(domain);
      
      logger.info(`Starting SSL certificate generation for domain: ${cleanDomain}, isChildPanel: ${isChildPanel}`);

      // Check if certificates already exist (including suffixed directories)
      const certPath = this._findCertificatePath(cleanDomain);
      if (certPath) {
        logger.info(`Certificates already exist for domain: ${cleanDomain} at path: ${certPath}`);
        await this._copyCertificates(cleanDomain, certPath);
        await this._generateHTTPSConfig(cleanDomain, isChildPanel);
        
        return {
          success: true,
          message: `Existing certificates found and copied for ${cleanDomain}`,
          isChildPanel: isChildPanel
        };
      }

      // Ensure HTTP configuration is up to date
      await this._ensureHTTPConfig(cleanDomain, isChildPanel);

      // Create certificate request
      await this._createCertificateRequest(cleanDomain);

      // Wait for certificate generation
      const result = await this._waitForCertificateGeneration(cleanDomain, isChildPanel);
      
      return {
        ...result,
        isChildPanel: isChildPanel
      };

    } catch (error) {
      logger.error(`Error generating SSL certificate for domain ${domain}: ${error.message}`);
      throw error;
    } finally {
      this.isCertbotRunning = false;
    }
  }

  /**
   * Delete SSL certificates for a domain
   * @param {string} domain - Domain name
   * @returns {Promise<boolean>} True if successful
   */
  async deleteCertificates(domain) {
    try {
      const cleanDomain = FileUtils.cleanDomainName(domain);
      logger.info(`Deleting SSL certificates for domain: ${cleanDomain}`);

      // Delete Nginx certificates
      this._deleteNginxCertificates(cleanDomain);

      // Delete Let's Encrypt certificates (including suffixed directories)
      this._deleteLetsEncryptCertificates(cleanDomain);

      // Delete renewal configuration (including suffixed configs)
      this._deleteRenewalConfig(cleanDomain);

      // Clean up request files
      this._cleanupRequestFiles(cleanDomain);

      return true;
    } catch (error) {
      logger.error(`Error deleting certificates for domain ${domain}: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find the correct certificate path for a domain (handles suffixed directories)
   * @param {string} domain - Domain name
   * @returns {string|null} Path to certificate directory or null if not found
   * @private
   */
  _findCertificatePath(domain) {
    const cleanDomain = FileUtils.cleanDomainName(domain);
    
    try {
      if (!fs.existsSync(config.LETSENCRYPT_PATH)) {
        return null;
      }

      const directories = fs.readdirSync(config.LETSENCRYPT_PATH);
      
      // Find all directories that match the domain pattern
      const matchingDirs = directories.filter(dir => {
        return dir === cleanDomain || dir.startsWith(`${cleanDomain}-`);
      });

      if (matchingDirs.length === 0) {
        return null;
      }

      // Sort by modification time (newest first) or by suffix number (highest first)
      matchingDirs.sort((a, b) => {
        // Extract suffix numbers for comparison
        const getNumber = (dir) => {
          const match = dir.match(/-(\d+)$/);
          return match ? parseInt(match[1]) : 0;
        };
        
        return getNumber(b) - getNumber(a);
      });

      const selectedDir = matchingDirs[0];
      const fullPath = path.join(config.LETSENCRYPT_PATH, selectedDir);
      
      // Verify that the required certificate files exist
      const fullchainPath = path.join(fullPath, 'fullchain.pem');
      const privkeyPath = path.join(fullPath, 'privkey.pem');
      
      if (FileUtils.filesExist([fullchainPath, privkeyPath])) {
        logger.info(`Found valid certificate directory: ${selectedDir} for domain: ${cleanDomain}`);
        return fullPath;
      }
      
      logger.warn(`Certificate files not found in directory: ${fullPath}`);
      return null;
      
    } catch (error) {
      logger.error(`Error finding certificate path for domain ${cleanDomain}: ${error.message}`);
      return null;
    }
  }

  /**
   * Copy certificates from Let's Encrypt to Nginx directory
   * @param {string} domain - Domain name
   * @param {string} certPath - Optional specific certificate path
   * @private
   */
  async _copyCertificates(domain, certPath = null) {
    const cleanDomain = FileUtils.cleanDomainName(domain);
    
    const nginxCertPath = path.join(config.SSL_PATH, `${cleanDomain}.crt`);
    const nginxKeyPath = path.join(config.SSL_PATH, `${cleanDomain}.key`);
    
    // Use provided path or find the correct path
    const sourcePath = certPath || this._findCertificatePath(cleanDomain);
    
    if (!sourcePath) {
      throw new Error(`No valid certificate directory found for domain: ${cleanDomain}`);
    }
    
    const letsEncryptCertPath = path.join(sourcePath, 'fullchain.pem');
    const letsEncryptKeyPath = path.join(sourcePath, 'privkey.pem');

    if (!FileUtils.filesExist([letsEncryptCertPath, letsEncryptKeyPath])) {
      throw new Error(`Certificates not found at: ${letsEncryptCertPath}`);
    }

    FileUtils.ensureDirectoryExists(config.SSL_PATH);
    FileUtils.copyFile(letsEncryptCertPath, nginxCertPath, `SSL certificate for ${cleanDomain}`);
    FileUtils.copyFile(letsEncryptKeyPath, nginxKeyPath, `SSL key for ${cleanDomain}`);
    
    logger.info(`Certificates copied from ${sourcePath} to ${config.SSL_PATH} for domain: ${cleanDomain}`);
  }

  /**
   * Check if certificates exist for a domain (including suffixed directories)
   * @param {string} domain - Domain name
   * @returns {boolean} True if certificates exist
   * @private
   */
  _certificatesExist(domain) {
    return this._findCertificatePath(domain) !== null;
  }

  /**
   * Ensure HTTP configuration is up to date
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @private
   */
  async _ensureHTTPConfig(domain, isChildPanel = false) {
    logger.info(`Ensuring HTTP configuration is up to date for domain: ${domain}, isChildPanel: ${isChildPanel}`);
    
    try {
      const domainResponse = await axios.get(`${config.DB_API}/${domain}`);
      if (domainResponse.data && domainResponse.data.data) {
        const tenant = domainResponse.data.data;
        if (isChildPanel) {
          // For child panel domains, we need to generate appropriate config
          await NginxConfigService.generateChildPanelConfig(tenant, false);
        } else {
          await NginxConfigService.generateConfig(tenant, false);
        }
      } else {
        throw new Error(`Could not get tenant data for domain: ${domain}`);
      }
    } catch (err) {
      logger.error(`Error getting tenant data for domain ${domain}: ${err.message}`);
      throw err;
    }

    // Wait for Nginx to reload
    await new Promise(resolve => setTimeout(resolve, config.SSL_GENERATION_WAIT));
    logger.info(`Nginx HTTP configuration refreshed for domain: ${domain}`);
  }

  /**
   * Create certificate request file
   * @param {string} domain - Domain name
   * @private
   */
  async _createCertificateRequest(domain) {
    FileUtils.ensureDirectoryExists(config.RENEWAL_REQUESTS_DIR);

    const requestFile = path.join(config.RENEWAL_REQUESTS_DIR, `${domain}.json`);
    const requestData = {
      domain: domain,
      email: config.ADMIN_EMAIL,
      timestamp: new Date().toISOString(),
      webroot: config.CERTBOT_WEBROOT
    };

    FileUtils.writeFile(requestFile, JSON.stringify(requestData, null, 2), `Certificate request for ${domain}`);
  }

  /**
   * Wait for certificate generation to complete
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object>} Result of the operation
   * @private
   */
  async _waitForCertificateGeneration(domain, isChildPanel = false) {
    let attempts = 0;
    const maxAttempts = config.SSL_MAX_ATTEMPTS;
    const waitTime = config.SSL_CHECK_INTERVAL;

    while (attempts < maxAttempts) {
      await new Promise(resolve => setTimeout(resolve, waitTime));
      attempts++;

      logger.info(`Checking for certificates (attempt ${attempts}/${maxAttempts})...`);

      // Check for success marker
      const successResult = await this._checkSuccessMarker(domain, isChildPanel);
      if (successResult) {
        return successResult;
      }

      // Check for failure marker
      const failureResult = await this._checkFailureMarker(domain);
      if (failureResult) {
        throw failureResult;
      }

      // Direct certificate check (now handles suffixed directories)
      const directResult = await this._checkDirectCertificates(domain, isChildPanel);
      if (directResult) {
        return directResult;
      }
    }

    throw new Error(`Timeout waiting for certificate generation for domain: ${domain}`);
  }

  /**
   * Check for success marker file
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object|null>} Success result or null
   * @private
   */
  async _checkSuccessMarker(domain, isChildPanel = false) {
    const successMarkerFile = path.join(config.RENEWAL_REQUESTS_DIR, `success-${domain}.json`);
    
    if (FileUtils.fileExists(successMarkerFile)) {
      logger.info(`Success marker file found for domain: ${domain}`);

      if (this._certificatesExist(domain)) {
        logger.info(`Certificates found for domain: ${domain}`);
        
        await this._copyCertificates(domain);
        this._cleanupRequestFiles(domain);
        await this._generateHTTPSConfig(domain, isChildPanel);

        return {
          success: true,
          message: `SSL certificate successfully generated for ${domain}`,
          isChildPanel: isChildPanel
        };
      } else {
        logger.info(`Success marker found but certificates not found for domain: ${domain}. Waiting...`);
      }
    }
    
    return null;
  }

  /**
   * Check for failure marker file
   * @param {string} domain - Domain name
   * @returns {Promise<Error|null>} Error object or null
   * @private
   */
  async _checkFailureMarker(domain) {
    const failureMarkerFile = path.join(config.RENEWAL_REQUESTS_DIR, `failure-${domain}.json`);
    
    if (FileUtils.fileExists(failureMarkerFile)) {
      logger.info(`Failure marker file found for domain: ${domain}`);

      let failureDetails = "Unknown error";
      let certbotOutput = "";
      
      try {
        const failureData = JSON.parse(FileUtils.readFile(failureMarkerFile));
        failureDetails = failureData.error || failureDetails;
        certbotOutput = failureData.certbot_output || "";
        logger.error(`Certbot error details: ${certbotOutput}`);
      } catch (err) {
        logger.error(`Error reading failure marker file: ${err.message}`);
      }

      this._cleanupRequestFiles(domain);

      const error = new Error(`Failed to generate certificate: ${failureDetails}`);
      error.certbotOutput = certbotOutput;
      return error;
    }
    
    return null;
  }

  /**
   * Check for certificates directly
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @returns {Promise<Object|null>} Success result or null
   * @private
   */
  async _checkDirectCertificates(domain, isChildPanel = false) {
    if (this._certificatesExist(domain)) {
      logger.info(`Certificates found for domain: ${domain} (no marker file)`);
      
      await this._copyCertificates(domain);
      this._cleanupRequestFiles(domain);
      await this._generateHTTPSConfig(domain, isChildPanel);

      return {
        success: true,
        message: `SSL certificate successfully generated for ${domain}`,
        isChildPanel: isChildPanel
      };
    }
    
    return null;
  }

  /**
   * Generate HTTPS configuration after certificate generation
   * @param {string} domain - Domain name
   * @param {boolean} isChildPanel - Whether this is for a child panel domain
   * @private
   */
  async _generateHTTPSConfig(domain, isChildPanel = false) {
    logger.info(`Generating HTTPS configuration for domain: ${domain}, isChildPanel: ${isChildPanel}`);
    
    try {
      const domainResponse = await axios.get(`${config.DB_API}/${domain}`);
      if (domainResponse.data && domainResponse.data.data) {
        const tenant = domainResponse.data.data;
        if (isChildPanel) {
          // Use child panel specific configuration generation
          await NginxConfigService.generateChildPanelConfig(tenant, true);
        } else {
          await NginxConfigService.generateConfig(tenant, true);
        }
      } else {
        logger.error(`Could not get tenant data for domain: ${domain}`);
      }
    } catch (err) {
      logger.error(`Error getting tenant data for domain ${domain}: ${err.message}`);
    }
  }

  /**
   * Delete Nginx certificates
   * @param {string} domain - Domain name
   * @private
   */
  _deleteNginxCertificates(domain) {
    const nginxCertPath = path.join(config.SSL_PATH, `${domain}.crt`);
    const nginxKeyPath = path.join(config.SSL_PATH, `${domain}.key`);

    FileUtils.deleteFile(nginxCertPath, `Nginx certificate for domain ${domain}`);
    FileUtils.deleteFile(nginxKeyPath, `Nginx key for domain ${domain}`);
  }

  /**
   * Delete Let's Encrypt certificates (including suffixed directories)
   * @param {string} domain - Domain name
   * @private
   */
  _deleteLetsEncryptCertificates(domain) {
    try {
      if (!fs.existsSync(config.LETSENCRYPT_PATH)) {
        return;
      }

      const directories = fs.readdirSync(config.LETSENCRYPT_PATH);
      
      // Find all directories that match the domain pattern
      const matchingDirs = directories.filter(dir => {
        return dir === domain || dir.startsWith(`${domain}-`);
      });

      matchingDirs.forEach(dir => {
        const domainCertPath = path.join(config.LETSENCRYPT_PATH, dir);
        FileUtils.deleteDirectory(domainCertPath, `Let's Encrypt certificate directory ${dir} for domain ${domain}`);
        logger.info(`Deleted certificate directory: ${dir}`);
      });
      
    } catch (error) {
      logger.error(`Error deleting Let's Encrypt certificates for domain ${domain}: ${error.message}`);
    }
  }

  /**
   * Delete renewal configuration (including suffixed configs)
   * @param {string} domain - Domain name
   * @private
   */
  _deleteRenewalConfig(domain) {
    try {
      const renewalDir = '/etc/letsencrypt/renewal';
      
      if (!fs.existsSync(renewalDir)) {
        return;
      }

      const files = fs.readdirSync(renewalDir);
      
      // Find all renewal config files that match the domain pattern
      const matchingFiles = files.filter(file => {
        return file === `${domain}.conf` || file.startsWith(`${domain}-`) && file.endsWith('.conf');
      });

      matchingFiles.forEach(file => {
        const renewalConfigPath = path.join(renewalDir, file);
        FileUtils.deleteFile(renewalConfigPath, `Let's Encrypt renewal configuration ${file} for domain ${domain}`);
        logger.info(`Deleted renewal config: ${file}`);
      });
      
    } catch (error) {
      logger.error(`Error deleting renewal configuration for domain ${domain}: ${error.message}`);
    }
  }

  /**
   * Clean up request files
   * @param {string} domain - Domain name
   * @private
   */
  _cleanupRequestFiles(domain) {
    if (!FileUtils.fileExists(config.RENEWAL_REQUESTS_DIR)) {
      return;
    }

    const requestFile = path.join(config.RENEWAL_REQUESTS_DIR, `${domain}.json`);
    const successMarkerFile = path.join(config.RENEWAL_REQUESTS_DIR, `success-${domain}.json`);
    const failureMarkerFile = path.join(config.RENEWAL_REQUESTS_DIR, `failure-${domain}.json`);

    FileUtils.deleteFile(requestFile, `Certificate request file for domain ${domain}`);
    FileUtils.deleteFile(successMarkerFile, `Success marker file for domain ${domain}`);
    FileUtils.deleteFile(failureMarkerFile, `Failure marker file for domain ${domain}`);
  }
}

module.exports = new SSLService();