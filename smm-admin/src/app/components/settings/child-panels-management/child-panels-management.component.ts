import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IconsModule } from '../../../icons/icons.module';
import { TranslateModule } from '@ngx-translate/core';
import { ChildPanelTenant, TenantService } from '../../../core/services/tenant.service';
import { ToastService } from '../../../core/services/toast.service';
import { HttpClient } from '@angular/common/http';
import { ConfigService } from '../../../core/services/config.service';
import { Subscription } from 'rxjs';
import { AdminMenuComponent, AdminMenuItem } from '../../common/admin-menu/admin-menu.component';
import { TimezonePipe } from '../../../shared/pipes/timezone.pipe';



@Component({
  selector: 'app-child-panels-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IconsModule,
    TimezonePipe,
    TranslateModule,
    AdminMenuComponent
  ],
  templateUrl: './child-panels-management.component.html',
  styleUrls: ['./child-panels-management.component.css']
})
export class ChildPanelsManagementComponent implements OnInit, OnDestroy {
  // Make Math available in template
  Math = Math;

  // Child panels data
  childPanels: ChildPanelTenant[] = [];
  searchTerm: string = '';
  
  // Loading state
  isLoading = false;

  // Current tenant
  currentTenantId: string | null = null;

  // Extend subscription popup
  showExtendSubscriptionPopup: boolean = false;
  selectedPanelId: string = '';
  selectedPanelDomain: string = '';
  extensionDays: number = 30;

  // Selected tenant for actions
  selectedTenant: ChildPanelTenant | null = null;

  private subscriptions: Subscription[] = [];

  constructor(
    private tenantService: TenantService,
    private toastService: ToastService,

  ) {}

  ngOnInit() {
    // Load child panels on init
    this.loadChildPanels();

    // Subscribe to current tenant changes to reload data
    this.subscriptions.push(
      this.tenantService.currentTenant$.subscribe(tenant => {
        if (tenant) {
          this.currentTenantId = typeof tenant === 'string' ? tenant : tenant.id;
          this.loadChildPanels();
        }
      })
    );
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Get menu actions for a panel based on its status
  getMenuActionsForPanel(panel: ChildPanelTenant): AdminMenuItem[] {
    const actions: AdminMenuItem[] = [];

    if (panel.status === 'Waiting') {
      actions.push(
        { id: 'approve', label: 'admin.child_panels.approve_panel', icon: 'check', iconColor: 'text-green-600' },
        { id: 'reject', label: 'admin.child_panels.reject_panel', icon: 'times', iconColor: 'text-red-600' }
      );
    } else if (panel.status === 'Active' || panel.status === 'Expired') {
      actions.push(
        { id: 'extend-subscription', label: 'admin.child_panels.extend_subscription', icon: 'calendar-plus', iconColor: 'text-blue-600' },
        { id: 'disable', label: 'admin.child_panels.disable_panel', icon: 'ban', iconColor: 'text-orange-600' }
      );
    } else if (panel.status === 'Suspended') {
      actions.push(
        { id: 'activate', label: 'admin.child_panels.activate_panel', icon: 'play', iconColor: 'text-green-600' }
      );
    }

    return actions;
  }

  // Handle menu action clicks
  handleMenuAction(actionId: string, panel: ChildPanelTenant) {
    switch (actionId) {
      case 'approve':
        this.approvePanel(panel);
        break;
      case 'reject':
        this.rejectPanel(panel);
        break;
      case 'extend-subscription':
        this.openExtendSubscriptionPopup(panel);
        break;
      case 'disable':
        this.disablePanel(panel);
        break;
      case 'activate':
        this.activatePanel(panel);
        break;
    }
  }

  // Load child panels for current tenant
  loadChildPanels() {
    this.isLoading = true;

    // Use current tenant context from backend, no need to pass tenant ID in URL
    this.tenantService.loadChildPanels().subscribe({
      next: (childPanels) => {
        this.childPanels = this.filterChildPanels(childPanels);
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading child panels:', error);
        this.toastService.showError('Failed to load child panels');
        this.isLoading = false;
      }
    });
  }

  // Filter child panels based on search term
  filterChildPanels(panels: ChildPanelTenant[]): ChildPanelTenant[] {
    if (!this.searchTerm.trim()) {
      return panels;
    }

    const searchLower = this.searchTerm.toLowerCase().trim();
    return panels.filter(panel => 
      panel.domain.toLowerCase().includes(searchLower) ||
      panel.contact_email?.toLowerCase().includes(searchLower) ||
      panel.status.toLowerCase().includes(searchLower)
    );
  }

  search() {
    this.loadChildPanels();
  }

  resetSearch() {
    this.searchTerm = '';
    this.loadChildPanels();
  }



  getStatusClass(status: string): string {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'waiting':
        return 'bg-yellow-100 text-yellow-800';
      case 'suspended':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-orange-100 text-orange-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  // Approve panel
  approvePanel(panel: ChildPanelTenant) {
    if (confirm(`Are you sure you want to approve the panel "${panel.domain}"?`)) {
      this.tenantService.approveChildPanel(panel.id).subscribe({
        next: (updatedPanel) => {
          this.toastService.showSuccess('Panel approved successfully');
          this.loadChildPanels(); // Reload the list
        },
        error: (error) => {
          console.error('Error approving panel:', error);
          this.toastService.showError(error.message || 'Failed to approve panel');
        }
      });
    }
  }

  // Reject panel
  rejectPanel(panel: ChildPanelTenant) {
    if (confirm(`Are you sure you want to reject the panel "${panel.domain}"?`)) {
      this.tenantService.rejectChildPanel(panel.id).subscribe({
        next: (updatedPanel) => {
          this.toastService.showSuccess('Panel rejected successfully');
          this.loadChildPanels(); // Reload the list
        },
        error: (error) => {
          console.error('Error rejecting panel:', error);
          this.toastService.showError(error?.message || 'Failed to reject panel');
        }
      });
    }
  }

  // Open extend subscription popup
  openExtendSubscriptionPopup(panel: ChildPanelTenant) {
    this.selectedPanelId = panel.id;
    this.selectedPanelDomain = panel.domain;
    this.extensionDays = 30; // Default to 30 days
    this.showExtendSubscriptionPopup = true;
  }

  // Close extend subscription popup
  closeExtendSubscriptionPopup() {
    this.showExtendSubscriptionPopup = false;
    this.selectedPanelId = '';
    this.selectedPanelDomain = '';
    this.extensionDays = 30;
  }

  // Extend subscription
  extendSubscription() {
    if (this.extensionDays <= 0) {
      this.toastService.showError('Extension days must be greater than 0');
      return;
    }

    this.tenantService.extendChildPanelSubscription(this.selectedPanelId, this.extensionDays).subscribe({
      next: (updatedPanel) => {
        this.toastService.showSuccess(`Subscription extended by ${this.extensionDays} days`);
        this.closeExtendSubscriptionPopup();
        this.loadChildPanels(); // Reload the list
      },
      error: (error) => {
        console.error('Error extending subscription:', error);
        this.toastService.showError(error?.message || 'Failed to extend subscription');
      }
    });
  }

  // Disable panel (suspend)
  disablePanel(panel: ChildPanelTenant) {
    if (confirm(`Are you sure you want to disable the panel "${panel.domain}"?`)) {
      this.tenantService.suspendChildPanel(panel.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Panel disabled successfully');
          this.loadChildPanels(); // Reload the list
        },
        error: (error) => {
          console.error('Error disabling panel:', error);
          this.toastService.showError('Failed to disable panel');
        }
      });
    }
  }

  // Activate panel
  activatePanel(panel: ChildPanelTenant) {
    if (confirm(`Are you sure you want to activate the panel "${panel.domain}"?`)) {
      this.tenantService.activateChildPanel(panel.id).subscribe({
        next: () => {
          this.toastService.showSuccess('Panel activated successfully');
          this.loadChildPanels(); // Reload the list
        },
        error: (error) => {
          console.error('Error activating panel:', error);
          this.toastService.showError('Failed to activate panel');
        }
      });
    }
  }

  deleteChildPanel(tenant: ChildPanelTenant) {
    if (confirm(`Are you sure you want to permanently delete child panel "${tenant.domain}"? This action cannot be undone.`)) {
      // Note: You might need to implement a delete endpoint in the backend
      this.toastService.showWarning('Delete functionality not implemented yet');
    }
  }

  // Get admin credentials from cache
  getAdminCredentials(domain: string): any {
    const credentialsKey = `child_panel_credentials_${domain}`;
    const stored = localStorage.getItem(credentialsKey);
    
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch (error) {
        console.error('Error parsing stored credentials:', error);
      }
    }
    
    return null;
  }

  // Show admin credentials
  showCredentials(tenant: ChildPanelTenant) {
    const credentials = this.getAdminCredentials(tenant.domain);
    
    if (credentials) {
      alert(`Admin Credentials for ${tenant.domain}:\nUsername: ${credentials.adminName}\nPassword: ${credentials.adminPassword}`);
    } else {
      this.toastService.showWarning('No cached credentials found for this child panel');
    }
  }
}
